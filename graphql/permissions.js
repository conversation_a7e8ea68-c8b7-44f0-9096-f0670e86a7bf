
const { shield, and, or, deny, allow } = require('graphql-shield');
const {
  isJwtAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurant,
  isCustomer,
  isResourceOwner,
  isOrderOwner,
  isSelfManagement
} = require('./permissions/rules');
const { logMissingPermission } = require('./permissions/logger');


const canBeAccessedByCustomer = or(isCustomer, isAdmin);
const canAccessOrder = or(isOrderOwner, isAdmin);



const permissions = shield(
  {
    Query: {
      
      restaurants: isJwtAuthenticated,
      restaurant: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantPreview: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantsPreview: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantList: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantListPreview: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      categories: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      foods: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      foodByCategory: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      foodByIds: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      banners: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      bannerActions: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      cuisines: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      addons: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      options: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      taxes: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      tips: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      zones: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      zone: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      coupons: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      offers: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      sections: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getCountries: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getCountryByIso: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      popularItems: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      relatedItems: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
    
      // === X-WhatsAppW-Token专用查询（受限访问）===
      
      customerAddresses: isWebWhatsAppToken,
      getAddressFromPostcode: isWebWhatsAppToken,
      getSessionByToken: isWebWhatsAppToken,

      // === 内部调用专用查询（系统内部使用）===
      customerbyPhone: allowInternalCall,
      customerbyPhoneAll: allowInternalCall,

      // === 客户查询 ===
      profile: and(isJwtAuthenticated, canBeAccessedByCustomer),
      userFavourite: and(isJwtAuthenticated, canBeAccessedByCustomer),

      // === 餐厅查询 ===
      orders: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      order: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      orderDetails: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantOrders: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      ordersByRestId: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      orderCount: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      pageCount: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getRefund: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getOrderRefunds: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getDashboardTotal: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getDashboardOrders: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getDashboardSales: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getOrdersByDateRange: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      getActiveOrders: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantByOwner: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      reviews: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),

      // === 品牌查询 ===
      brand: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      brands: and(isJwtAuthenticated, isAdmin),

      // === 管理员专用查询 ===
      users: and(isJwtAuthenticated, isAdmin),
      configuration: and(isJwtAuthenticated, isAdmin),
      allOrders: and(isJwtAuthenticated, isAdmin),
      assignedOrders: and(isJwtAuthenticated, isAdmin),
      deliveredOrders: and(isJwtAuthenticated, isAdmin),
      undeliveredOrders: and(isJwtAuthenticated, isAdmin),
      getOrderStatuses: and(isJwtAuthenticated, isAdmin),
      getPaymentStatuses: and(isJwtAuthenticated, isAdmin),
      vendors: and(isJwtAuthenticated, isAdmin),
      getVendor: and(isJwtAuthenticated, isAdmin),
      earnings: and(isJwtAuthenticated, isAdmin),
      withdrawRequests: and(isJwtAuthenticated, isAdmin),
      getAllWithdrawRequests: and(isJwtAuthenticated, isAdmin),

      // === 骑手管理查询（暂时不用）===
      rider: and(isJwtAuthenticated, isAdmin),
      riders: and(isJwtAuthenticated, isAdmin),
      ridersByZone: and(isJwtAuthenticated, isAdmin),
      riderCompletedOrders: and(isJwtAuthenticated, isAdmin),
      riderEarnings: and(isJwtAuthenticated, isAdmin),
      riderOrders: and(isJwtAuthenticated, isAdmin),
      riderWithdrawRequests: and(isJwtAuthenticated, isAdmin),
      availableRiders: and(isJwtAuthenticated, isAdmin),
      unassignedOrdersByZone: and(isJwtAuthenticated, isAdmin),

      // === 其他需要认证的查询 (暂时不用) ===
      likedFood: isJwtAuthenticated,
      orderPaypal: and(isJwtAuthenticated, isAdmin),
      orderStripe: and(isJwtAuthenticated, isAdmin),

      // === 地理位置相关查询（暂时不用)===
      nearByRestaurants: and(isJwtAuthenticated, isAdmin),
      nearByRestaurantsPreview: or(isRestaurant, isAdmin),
      topRatedVendors: or(isRestaurant, isAdmin),
      topRatedVendorsPreview: or(isRestaurant, isAdmin),
      recentOrderRestaurants: or(isRestaurant, isAdmin),
      recentOrderRestaurantsPreview: or(isRestaurant, isAdmin),
      mostOrderedRestaurants: or(isRestaurant, isAdmin),
      mostOrderedRestaurantsPreview: or(isRestaurant, isAdmin),

      // === 聊天查询 ===
      chat: and(isJwtAuthenticated, canAccessOrder),

      // === 演示查询（管理员专用）===
      lastOrderCreds: and(isJwtAuthenticated, isAdmin)
    },

    Mutation: {
      // === 公开变更 (注册等) ===
      createUser: and(isJwtAuthenticated, isAdmin),
      login: allow,

      // === X-WhatsAppW-Token专用变更（受限访问）===
      placeOrderWhatsApp: isWebWhatsAppToken,

      // === 客户变更 ===
      updateUser: and(isJwtAuthenticated, canBeManagedByUser),
      placeOrder: and(isJwtAuthenticated, canBeAccessedByCustomer),
      addCustomerAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      deleteCustomerAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      createAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      editAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      deleteAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      deleteBulkAddresses: and(isJwtAuthenticated, canBeAccessedByCustomer),
      selectAddress: and(isJwtAuthenticated, canBeAccessedByCustomer),
      addFavourite: and(isJwtAuthenticated, canBeAccessedByCustomer),
      updateNotificationStatus: and(isJwtAuthenticated, canBeAccessedByCustomer),
      pushToken: and(isJwtAuthenticated, canBeAccessedByCustomer),
      changePassword: and(isJwtAuthenticated, canBeManagedByUser),
      reviewOrder: and(isJwtAuthenticated, canBeAccessedByCustomer),

      // === 餐厅变更 ===
      updateOrderStatus: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      refundOrder: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      createRestaurant: and(isJwtAuthenticated, or(isRestaurant, isAdmin)),
      editRestaurant: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      deleteRestaurant: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      createFood: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      editFood: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      deleteFood: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      createCategory: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      editCategory: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      deleteCategory: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      createAddons: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      editAddon: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      deleteAddon: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      createOptions: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      editOption: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      deleteOption: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      toggleAvailability: and(isJwtAuthenticated, or(isRestaurant, isAdmin)),
      toggleMenuFood: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      saveRestaurantToken: and(isJwtAuthenticated, or(isRestaurant, isAdmin)),
      updateTimings: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      updateDeliveryBoundsAndLocation: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      restaurantLogin: allow,
      acceptOrder: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      orderPickedUp: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      abortOrder: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      cancelOrder: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),

      // === 品牌变更 ===
      createBrand: and(isJwtAuthenticated, isAdmin),
      updateBrand: and(isJwtAuthenticated, isAdmin),
      deleteBrand: and(isJwtAuthenticated, isAdmin),
      addRestaurantToBrand: and(isJwtAuthenticated, isAdmin),
      removeRestaurantFromBrand: and(isJwtAuthenticated, isAdmin),

      // === 内容管理变更（管理员专用）===
      createBanner: and(isJwtAuthenticated, isAdmin),
      editBanner: and(isJwtAuthenticated, isAdmin),
      deleteBanner: and(isJwtAuthenticated, isAdmin),
      createCuisine: and(isJwtAuthenticated, isAdmin),
      editCuisine: and(isJwtAuthenticated, isAdmin),
      deleteCuisine: and(isJwtAuthenticated, isAdmin),

      // === 管理员专用变更 ===
      Deactivate: and(isJwtAuthenticated, isAdmin),
      adminLogin: allow,
      ownerLogin: allow,
      saveEmailConfiguration: and(isJwtAuthenticated, isAdmin),
      saveFormEmailConfiguration: and(isJwtAuthenticated, isAdmin),
      saveSendGridConfiguration: and(isJwtAuthenticated, isAdmin),
      savePaypalConfiguration: and(isJwtAuthenticated, isAdmin),
      saveStripeConfiguration: and(isJwtAuthenticated, isAdmin),
      saveTwilioConfiguration: and(isJwtAuthenticated, isAdmin),
      saveFirebaseConfiguration: and(isJwtAuthenticated, isAdmin),
      saveSentryConfiguration: and(isJwtAuthenticated, isAdmin),
      saveGoogleApiKeyConfiguration: and(isJwtAuthenticated, isAdmin),
      saveCloudinaryConfiguration: and(isJwtAuthenticated, isAdmin),
      saveAmplitudeApiKeyConfiguration: and(isJwtAuthenticated, isAdmin),
      saveGoogleClientIDConfiguration: and(isJwtAuthenticated, isAdmin),
      saveWebConfiguration: and(isJwtAuthenticated, isAdmin),
      saveAppConfigurations: and(isJwtAuthenticated, isAdmin),
      saveDemoConfiguration: and(isJwtAuthenticated, isAdmin),
      saveCurrencyConfiguration: and(isJwtAuthenticated, isAdmin),
      saveDeliveryRateConfiguration: and(isJwtAuthenticated, isAdmin),
      saveVerificationsToggle: and(isJwtAuthenticated, isAdmin),
      updateCommission: and(isJwtAuthenticated, isAdmin),

      // === 系统管理变更（管理员专用）===
      createVendor: and(isJwtAuthenticated, isAdmin),
      editVendor: and(isJwtAuthenticated, isAdmin),
      deleteVendor: and(isJwtAuthenticated, isAdmin),
      uploadToken: and(isJwtAuthenticated, isAdmin),
      vendorResetPassword: and(isJwtAuthenticated, isAdmin),
      createZone: and(isJwtAuthenticated, isAdmin),
      editZone: and(isJwtAuthenticated, isAdmin),
      deleteZone: and(isJwtAuthenticated, isAdmin),
      createOffer: and(isJwtAuthenticated, isAdmin),
      editOffer: and(isJwtAuthenticated, isAdmin),
      deleteOffer: and(isJwtAuthenticated, isAdmin),
      addRestaurantToOffer: and(isJwtAuthenticated, isAdmin),
      createSection: and(isJwtAuthenticated, isAdmin),
      editSection: and(isJwtAuthenticated, isAdmin),
      deleteSection: and(isJwtAuthenticated, isAdmin),
      createTaxation: and(isJwtAuthenticated, isAdmin),
      editTaxation: and(isJwtAuthenticated, isAdmin),
      createTipping: and(isJwtAuthenticated, isAdmin),
      editTipping: and(isJwtAuthenticated, isAdmin),
      createCoupon: and(isJwtAuthenticated, isAdmin),
      editCoupon: and(isJwtAuthenticated, isAdmin),
      deleteCoupon: and(isJwtAuthenticated, isAdmin),
      coupon: and(isJwtAuthenticated, isAdmin),
      banner: and(isJwtAuthenticated, isAdmin),
      cuisine: and(isJwtAuthenticated, isAdmin),

      // === 骑手管理变更（管理员专用）===
      createRider: and(isJwtAuthenticated, isAdmin),
      editRider: and(isJwtAuthenticated, isAdmin),
      deleteRider: and(isJwtAuthenticated, isAdmin),
      riderLogin: allow,
      toggleAvailablity: and(isJwtAuthenticated, isAdmin),
      assignOrder: and(isJwtAuthenticated, isAdmin),
      assignRider: and(isJwtAuthenticated, isAdmin),
      updateOrderStatusRider: and(isJwtAuthenticated, isAdmin),
      updateRiderLocation: and(isJwtAuthenticated, isAdmin),
      notifyRiders: and(isJwtAuthenticated, isAdmin),

      // === 收益和提现管理（管理员专用）===
      createEarning: and(isJwtAuthenticated, isAdmin),
      createWithdrawRequest: and(isJwtAuthenticated, isAdmin),
      updateWithdrawReqStatus: and(isJwtAuthenticated, isAdmin),

      // === 聊天变更 ===
      sendChatMessage: and(isJwtAuthenticated, canAccessOrder),

      // === 通知和表单变更 ===
      sendNotificationUser: and(isJwtAuthenticated, isAdmin),
      saveNotificationTokenWeb: isJwtAuthenticated,
      sendFormSubmission: allow,
      sendOtpToEmail: allow,
      sendOtpToPhoneNumber: allow,
      emailExist: allow,
      phoneExist: allow,
      forgotPassword: allow,
      resetPassword: allow,
      muteRing: and(isJwtAuthenticated, canAccessOrder),

      // === 其他需要认证的变更 ===
      editOrder: and(isJwtAuthenticated, canAccessOrder),
      likeFood: and(isJwtAuthenticated, canBeAccessedByCustomer),
      updateStatus: and(isJwtAuthenticated, canAccessOrder),
      updatePaymentStatus: and(isJwtAuthenticated, canAccessOrder)
    },

    Subscription: {
      // === 订单状态订阅 ===
      SubscribeOrderStatus: and(isJwtAuthenticated, canBeAccessedByCustomer),
      subscribePlaceOrder: and(isJwtAuthenticated, or(isRestaurant, isAdmin), isResourceOwner),
      subscriptionOrder: and(isJwtAuthenticated, canAccessOrder),

      // === 骑手相关订阅（管理员专用）===
      subscriptionAssignRider: and(isJwtAuthenticated, isAdmin),
      subscriptionRiderLocation: and(isJwtAuthenticated, isAdmin),
      subscriptionDispatcher: and(isJwtAuthenticated, isAdmin),
      subscriptionZoneOrders: and(isJwtAuthenticated, isAdmin),

      // === 聊天订阅 ===
      subscriptionNewMessage: and(isJwtAuthenticated, canAccessOrder)
    },

    // === 类型字段权限 ===
    // 对于大多数类型，如果能访问类型本身，就应该能访问其所有字段
    // 使用通配符 "*" 来简化配置
    CustomerAddress: allow,
    Coordinates: allow,
    Customer: allow,
    User: allow,
    Restaurant: allow,
    Order: allow,
    Food: allow,
    Category: allow,
    Addon: allow,
    Option: allow,
    Zone: allow,
    Banner: allow,
    Coupon: allow,
    Offer: allow,
    Section: allow,
    Country: allow,
    City: allow,
    Brand: allow,
    RestaurantBrief: allow,
    Rider: allow,
    Earnings: allow,
    WithdrawRequest: allow,
    Point: allow,
    GeoPoint: allow,
    Polygon: allow,
    Location: allow,
    Admin: allow,
    AuthData: allow,
    Owner: allow,
    OwnerAuthData: allow,
    OwnerData: allow,
    RestaurantAuth: allow,
    Address: allow,
    AddressDetails: allow,
    OrderBrief: allow,
    PopularItemsResponse: allow,
    DemoCredentails: allow
  },
  {
    fallbackRule: deny, // 默认拒绝所有未明确允许的访问
    allowExternalErrors: true, // 允许将规则中的Error消息返回给客户端
    debug: process.env.NODE_ENV !== 'production', // 在非生产环境开启调试信息
    fallbackError: (_thrownThing, _parent, _args, context, info) => {
      // 记录权限被拒绝的详细信息
      const operation = info.operation.operation; // query, mutation, subscription
      const fieldName = info.fieldName;

      if (process.env.NODE_ENV !== 'production') {
        logMissingPermission(operation, fieldName, context);
      }

      return new Error(`Not Authorised! Missing permission for ${operation}.${fieldName}`);
    }
  }
);

module.exports = permissions;
